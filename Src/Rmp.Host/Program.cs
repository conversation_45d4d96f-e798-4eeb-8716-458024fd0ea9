using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Rmp.Infrastructure;
using Rmp.Jobs;
using Rmp.Scrapper;
using Rmp.Telegram;
using Serilog;

Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .CreateLogger();


var builder = Host.CreateApplicationBuilder();

builder.Configuration.AddJsonFile("appsettings.json");

builder.Services.AddSerilog();

builder.Services.AddInfrastructure(builder.Configuration);

builder.Services.AddScrapper();

builder.Services.AddTelegram(builder.Configuration);

builder.Services.AddJobs();

var host = builder.Build();

await host.RunAsync();